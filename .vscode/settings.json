{"editor.tabSize": 2, "editor.insertSpaces": true, "editor.renderWhitespace": "boundary", "editor.rulers": [140], "editor.formatOnSave": true, "files.encoding": "utf8", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "search.exclude": {"public/**": true, "node_modules/**": true}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "cSpell.language": "en", "cSpell.words": ["vite", "vitest"]}