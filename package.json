{"name": "@gaco/gaco-library", "version": "0.34.2", "description": "GACO components library, for use in GACO projects.", "type": "module", "types": "dist/index.d.ts", "scripts": {"build": "tsc && vite build", "build:storybook": "storybook build", "check:format": "eslint . --max-warnings 0", "check:types": "tsc --noEmit", "check:size": "npm run build && size-limit", "prepare-publish": "cp package.json dist/ && cp README.md dist/", "publish:pkg": "npm run prepare-publish && npm publish dist/ --access public", "release:major": "standard-version --release-as major && git push --follow-tags", "release:minor": "standard-version --release-as minor && git push --follow-tags", "release:patch": "standard-version --release-as patch && git push --follow-tags", "release:create-github-release": "conventional-github-releaser -p angular", "serve": "vite preview", "start": "storybook dev -p 6007 --ci", "storybook": "storybook dev -p 6007 --ci", "test": "vitest", "coverage": "vitest --coverage", "upgrade-interactive": "npm-check -u"}, "repository": {"type": "git", "url": "git+https://github.com/Gast-Art/gaco-library.git"}, "author": "openscript GmbH <<EMAIL>>", "license": "MIT", "main": "./index.umd.js", "module": "./index.es.js", "exports": {".": {"import": "./index.es.js", "require": "./index.umd.js"}}, "size-limit": [{"path": "./dist/index.es.js"}, {"path": "./dist/index.umd.js"}], "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "devDependencies": {"@babel/core": "^7.26.7", "@size-limit/preset-small-lib": "^11.1.6", "@storybook/addon-actions": "^8.5.1", "@storybook/addon-docs": "^8.5.1", "@storybook/addon-essentials": "^8.5.1", "@storybook/addon-links": "^8.5.1", "@storybook/addon-storysource": "^8.5.1", "@storybook/addon-themes": "^8.5.1", "@storybook/react": "^8.5.1", "@storybook/react-vite": "^8.5.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "@vitejs/plugin-react": "^4.3.4", "babel-loader": "^9.2.1", "conventional-github-releaser": "^3.1.5", "eslint": "^9.19.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-storybook": "^0.11.2", "npm-check": "^6.0.1", "prettier": "^3.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "size-limit": "^11.1.6", "standard-version": "^9.5.0", "storybook": "^8.5.1", "typescript": "^5.7.3", "vite": "^6.0.11", "vite-plugin-dts": "^4.5.0", "vite-plugin-markdown": "^2.2.0", "vitest": "^3.0.4"}, "dependencies": {"@gaco/gaco-library": "^0.29.4", "@hookform/resolvers": "^4.1.3", "@maptiler/sdk": "^3.0.1", "@radix-ui/react-dialog": "^1.1.6", "@storybook/addon-backgrounds": "^8.6.0", "@storybook/theming": "^8.5.3", "@tanstack/react-table": "^8.21.3", "ag-grid-community": "^34.0.0", "ag-grid-react": "^34.0.0", "date-fns": "^4.1.0", "lucide-react": "^0.476.0", "next": "^15.3.5", "radix-ui": "^1.1.3", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-select": "^5.10.1", "recharts": "^2.15.1", "styled-components": "^6.1.15", "styled-normalize": "^8.1.1", "yup": "^1.6.1"}, "directories": {"doc": "docs"}, "bugs": {"url": "https://github.com/openscript-ch/gaco-library/issues"}, "homepage": "https://github.com/openscript-ch/gaco-library#readme", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}