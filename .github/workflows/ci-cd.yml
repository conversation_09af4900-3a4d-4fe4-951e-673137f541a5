name: CI/CD

on:
  push:
    tags:
      - v*

jobs:
  build-and-deploy:
    name: Build and deploy NPM package
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: '1'

      - name: Configure node for npmjs.org as registry
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'
          registry-url: 'https://registry.npmjs.org'

      - name: Install dependencies
        run: yarn install --mode=skip-build

      - name: Check format
        run: yarn check:format

      - name: Check types
        run: yarn check:types

      - name: Run tests
        run: yarn test

      - name: Build
        run: yarn build

      - name: Create Github release
        run: yarn release:create-github-release
        env:
          CONVENTIONAL_GITHUB_RELEASER_TOKEN: ${{secrets.GITHUB_TOKEN}}

      - name: Publish package on npmjs.org
        run: npm run publish:pkg
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPMDEPLOYKEY }}

      - name: Build storybook
        run: yarn build:storybook

      - name: Deploy to Github Pages
        uses: JamesIves/github-pages-deploy-action@v4.3.4
        with:
          branch: gh-pages
          folder: storybook-static
          clean: true
