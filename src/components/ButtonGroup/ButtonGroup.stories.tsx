import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { But<PERSON> } from '../Button/Button';
import { ButtonGroup } from './ButtonGroup';

const meta: Meta<typeof ButtonGroup> = {
  component: ButtonGroup,
  title: 'Molecules/ButtonGroup',
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {
  args: {
    children: (
      <>
        <Button>Button One</Button>
        <Button>Button Two</Button>
      </>
    ),
  },
};

export const Three: Story = {
  args: {
    children: (
      <>
        <Button>Button One</Button>
        <Button>Button Two</Button>
        <Button variant="outline">Button Three</Button>
      </>
    ),
  },
};
