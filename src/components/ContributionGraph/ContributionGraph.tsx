import React, { useMemo, useState } from 'react';
import { format, eachDayOfInterval, parseISO, startOfWeek, endOfWeek, getDay, subDays, addDays } from 'date-fns';
import { ContributionGraphProps, ContributionCell, TooltipData } from './types';
import { StyledContributionGraph, StyledCell, StyledTooltip, StyledLegend } from './ContributionGraph.styles';

export const ContributionGraph: React.FC<ContributionGraphProps> = ({
  data,
  producer,
  width = 800,
  height = 200,
  cellSize = 12,
  cellGap = 2,
  showTooltip = true,
  showLegend = true,
  startDate,
  endDate,
  colorScheme = 'green',
  maxValue,
}) => {
  const [tooltip, setTooltip] = useState<TooltipData | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const { cells, maxDataValue, weekLabels, monthLabels } = useMemo(() => {
    // Filter data for the specific producer
    const producerData = data.filter(d => d.producer === producer);
    
    // Create a map for quick lookup
    const dataMap = new Map<string, number>();
    producerData.forEach(d => {
      dataMap.set(d.date, d.value);
    });

    // Determine date range
    const now = new Date();
    const defaultStartDate = subDays(now, 364); // ~1 year ago
    const defaultEndDate = now;
    
    const start = startDate ? parseISO(startDate) : defaultStartDate;
    const end = endDate ? parseISO(endDate) : defaultEndDate;

    // Get all dates in the range
    const allDates = eachDayOfInterval({ start, end });
    
    // Calculate max value for color scaling
    const maxVal = maxValue || Math.max(...producerData.map(d => d.value), 1);

    // Create cells data
    const cellsData: ContributionCell[] = [];
    const weekLabelsData: string[] = [];
    const monthLabelsData: { label: string; x: number }[] = [];

    // Start from the beginning of the week containing the start date
    const weekStart = startOfWeek(start, { weekStartsOn: 0 }); // Sunday
    const weekEnd = endOfWeek(end, { weekStartsOn: 0 });
    const allWeekDates = eachDayOfInterval({ start: weekStart, end: weekEnd });

    let currentMonth = '';
    let weekIndex = 0;

    allWeekDates.forEach((date, index) => {
      const dateStr = format(date, 'yyyy-MM-dd');
      const value = dataMap.get(dateStr) || 0;
      const level = value === 0 ? 0 : Math.min(Math.ceil((value / maxVal) * 4), 4);
      
      const dayOfWeek = getDay(date);
      const week = Math.floor(index / 7);
      
      // Track week labels (show every 2 weeks)
      if (dayOfWeek === 0 && week % 2 === 0) {
        weekLabelsData.push(format(date, 'MMM d'));
      }

      // Track month labels
      const monthLabel = format(date, 'MMM');
      if (monthLabel !== currentMonth && dayOfWeek === 0) {
        monthLabelsData.push({
          label: monthLabel,
          x: week * (cellSize + cellGap)
        });
        currentMonth = monthLabel;
      }

      cellsData.push({
        date: dateStr,
        value,
        level,
        x: week * (cellSize + cellGap),
        y: dayOfWeek * (cellSize + cellGap)
      });
    });

    return {
      cells: cellsData,
      maxDataValue: maxVal,
      weekLabels: weekLabelsData,
      monthLabels: monthLabelsData
    };
  }, [data, producer, startDate, endDate, maxValue, cellSize, cellGap]);

  const handleCellHover = (cell: ContributionCell, event: React.MouseEvent) => {
    if (!showTooltip) return;
    
    setTooltip({
      date: cell.date,
      value: cell.value,
      producer,
      formattedDate: format(parseISO(cell.date), 'EEEE, MMMM d, yyyy')
    });
    setMousePosition({ x: event.clientX, y: event.clientY });
  };

  const handleCellLeave = () => {
    setTooltip(null);
  };

  const getLevelColor = (level: number): string => {
    const schemes = {
      green: ['#ebedf0', '#9be9a8', '#40c463', '#30a14e', '#216e39'],
      blue: ['#ebedf0', '#9ecbff', '#0969da', '#0550ae', '#033d8b'],
      purple: ['#ebedf0', '#e1bee7', '#a855f7', '#7c3aed', '#5b21b6'],
      orange: ['#ebedf0', '#fed7aa', '#fb923c', '#ea580c', '#c2410c']
    };
    return schemes[colorScheme][level];
  };

  return (
    <StyledContributionGraph width={width} height={height}>
      <svg width={width} height={height}>
        {/* Month labels */}
        {monthLabels.map((month, index) => (
          <text
            key={index}
            x={month.x}
            y={10}
            fontSize="10"
            fill="currentColor"
            opacity={0.6}
          >
            {month.label}
          </text>
        ))}
        
        {/* Day labels */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
          <text
            key={day}
            x={-10}
            y={20 + index * (cellSize + cellGap) + cellSize / 2}
            fontSize="9"
            fill="currentColor"
            opacity={0.6}
            textAnchor="end"
            dominantBaseline="middle"
          >
            {index % 2 === 1 ? day : ''} {/* Show only Mon, Wed, Fri */}
          </text>
        ))}

        {/* Contribution cells */}
        <g transform="translate(20, 20)">
          {cells.map((cell, index) => (
            <StyledCell
              key={index}
              x={cell.x}
              y={cell.y}
              width={cellSize}
              height={cellSize}
              fill={getLevelColor(cell.level)}
              onMouseEnter={(e) => handleCellHover(cell, e)}
              onMouseLeave={handleCellLeave}
              style={{ cursor: showTooltip ? 'pointer' : 'default' }}
            />
          ))}
        </g>
      </svg>

      {/* Legend */}
      {showLegend && (
        <StyledLegend>
          <span>Less</span>
          {[0, 1, 2, 3, 4].map(level => (
            <div
              key={level}
              style={{
                width: cellSize,
                height: cellSize,
                backgroundColor: getLevelColor(level),
                border: '1px solid rgba(27,31,35,0.06)'
              }}
            />
          ))}
          <span>More</span>
        </StyledLegend>
      )}

      {/* Tooltip */}
      {tooltip && showTooltip && (
        <StyledTooltip
          style={{
            left: mousePosition.x + 10,
            top: mousePosition.y - 10
          }}
        >
          <div><strong>{tooltip.value} sales</strong></div>
          <div>on {tooltip.formattedDate}</div>
          <div>by {tooltip.producer}</div>
        </StyledTooltip>
      )}
    </StyledContributionGraph>
  );
};
