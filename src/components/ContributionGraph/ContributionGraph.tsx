import React, { useMemo, useState } from 'react';
import { format, eachDayOfInterval, parseISO, startOfWeek, endOfWeek, getDay, subDays, addDays } from 'date-fns';
import { ContributionGraphProps, ContributionCell, TooltipData, ProducerContribution, ContributionData } from './types';
import { StyledContributionGraph, StyledCell, StyledTooltip, StyledLegend } from './ContributionGraph.styles';

export const ContributionGraph: React.FC<ContributionGraphProps> = ({
  data,
  producers,
  mode = 'stacked',
  producer,
  width = 800,
  height = 200,
  cellSize = 12,
  cellGap = 2,
  showTooltip = true,
  showLegend = true,
  startDate,
  endDate,
  colorScheme = 'green',
  maxValue,
}) => {
  const [tooltip, setTooltip] = useState<TooltipData | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const { cells, maxDataValue, weekLabels, monthLabels } = useMemo(() => {
    // Create producer color map
    const producerColorMap = new Map<string, string>();
    producers.forEach(p => {
      producerColorMap.set(p.name, p.color);
    });

    // Determine date range
    const now = new Date();
    const defaultStartDate = subDays(now, 364); // ~1 year ago
    const defaultEndDate = now;

    const start = startDate ? parseISO(startDate) : defaultStartDate;
    const end = endDate ? parseISO(endDate) : defaultEndDate;

    // Get all dates in the range
    const allDates = eachDayOfInterval({ start, end });

    // Group data by date
    const dataByDate = new Map<string, ContributionData[]>();
    data.forEach(d => {
      const dateStr = d.date;
      if (!dataByDate.has(dateStr)) {
        dataByDate.set(dateStr, []);
      }
      dataByDate.get(dateStr)!.push(d);
    });

    // Calculate max value for color scaling
    let maxVal = 0;
    if (mode === 'single' && producer) {
      const producerData = data.filter(d => d.producer === producer);
      maxVal = maxValue || Math.max(...producerData.map(d => d.value), 1);
    } else {
      // For stacked mode, calculate max total value per day
      dataByDate.forEach(dayData => {
        const totalValue = dayData.reduce((sum, d) => sum + d.value, 0);
        maxVal = Math.max(maxVal, totalValue);
      });
      maxVal = maxValue || maxVal || 1;
    }

    // Create cells data
    const cellsData: ContributionCell[] = [];
    const weekLabelsData: string[] = [];
    const monthLabelsData: { label: string; x: number }[] = [];

    // Start from the beginning of the week containing the start date
    const weekStart = startOfWeek(start, { weekStartsOn: 0 }); // Sunday
    const weekEnd = endOfWeek(end, { weekStartsOn: 0 });
    const allWeekDates = eachDayOfInterval({ start: weekStart, end: weekEnd });

    let currentMonth = '';
    let weekIndex = 0;

    allWeekDates.forEach((date, index) => {
      const dateStr = format(date, 'yyyy-MM-dd');
      const dayData = dataByDate.get(dateStr) || [];

      // Calculate contributions by producer
      const contributions: ProducerContribution[] = [];
      let totalValue = 0;

      if (mode === 'single' && producer) {
        // Single producer mode
        const producerEntry = dayData.find(d => d.producer === producer);
        const value = producerEntry?.value || 0;
        totalValue = value;
        if (value > 0) {
          contributions.push({
            producer,
            value,
            color: producerColorMap.get(producer) || '#ccc'
          });
        }
      } else {
        // Stacked mode - include all producers
        producers.forEach(p => {
          const producerEntry = dayData.find(d => d.producer === p.name);
          const value = producerEntry?.value || 0;
          totalValue += value;
          if (value > 0) {
            contributions.push({
              producer: p.name,
              value,
              color: p.color
            });
          }
        });
      }

      const level = totalValue === 0 ? 0 : Math.min(Math.ceil((totalValue / maxVal) * 4), 4);

      const dayOfWeek = getDay(date);
      const week = Math.floor(index / 7);

      // Track week labels (show every 2 weeks)
      if (dayOfWeek === 0 && week % 2 === 0) {
        weekLabelsData.push(format(date, 'MMM d'));
      }

      // Track month labels
      const monthLabel = format(date, 'MMM');
      if (monthLabel !== currentMonth && dayOfWeek === 0) {
        monthLabelsData.push({
          label: monthLabel,
          x: week * (cellSize + cellGap)
        });
        currentMonth = monthLabel;
      }

      cellsData.push({
        date: dateStr,
        totalValue,
        contributions,
        level,
        x: week * (cellSize + cellGap),
        y: dayOfWeek * (cellSize + cellGap)
      });
    });

    return {
      cells: cellsData,
      maxDataValue: maxVal,
      weekLabels: weekLabelsData,
      monthLabels: monthLabelsData
    };
  }, [data, producers, mode, producer, startDate, endDate, maxValue, cellSize, cellGap]);

  const handleCellHover = (cell: ContributionCell, event: React.MouseEvent) => {
    if (!showTooltip) return;

    const rect = (event.currentTarget as SVGElement).getBoundingClientRect();
    const containerRect = (event.currentTarget.closest('div') as HTMLElement).getBoundingClientRect();

    setTooltip({
      date: cell.date,
      totalValue: cell.totalValue,
      contributions: cell.contributions,
      formattedDate: format(parseISO(cell.date), 'EEEE, MMMM d, yyyy')
    });

    // Position tooltip relative to the container
    setMousePosition({
      x: rect.left - containerRect.left + rect.width / 2,
      y: rect.top - containerRect.top - 10
    });
  };

  const handleCellLeave = () => {
    setTooltip(null);
  };

  const getLevelColor = (level: number): string => {
    const schemes = {
      green: ['#ebedf0', '#9be9a8', '#40c463', '#30a14e', '#216e39'],
      blue: ['#ebedf0', '#9ecbff', '#0969da', '#0550ae', '#033d8b'],
      purple: ['#ebedf0', '#e1bee7', '#a855f7', '#7c3aed', '#5b21b6'],
      orange: ['#ebedf0', '#fed7aa', '#fb923c', '#ea580c', '#c2410c']
    };
    return schemes[colorScheme][level];
  };

  const renderCell = (cell: ContributionCell, index: number) => {
    if (mode === 'single') {
      // Single producer mode - use level-based coloring
      return (
        <StyledCell
          key={index}
          x={cell.x}
          y={cell.y}
          width={cellSize}
          height={cellSize}
          fill={getLevelColor(cell.level)}
          onMouseEnter={(e) => handleCellHover(cell, e)}
          onMouseLeave={handleCellLeave}
          style={{ cursor: showTooltip ? 'pointer' : 'default' }}
        />
      );
    } else {
      // Stacked mode - create segments for each producer
      if (cell.contributions.length === 0) {
        return (
          <StyledCell
            key={index}
            x={cell.x}
            y={cell.y}
            width={cellSize}
            height={cellSize}
            fill="#ebedf0"
            onMouseEnter={(e) => handleCellHover(cell, e)}
            onMouseLeave={handleCellLeave}
            style={{ cursor: showTooltip ? 'pointer' : 'default' }}
          />
        );
      }

      // Calculate proportions for stacking
      const segments: React.ReactElement[] = [];
      let currentY = 0;

      cell.contributions.forEach((contribution, contribIndex) => {
        const proportion = contribution.value / cell.totalValue;
        const segmentHeight = cellSize * proportion;

        segments.push(
          <rect
            key={`${index}-${contribIndex}`}
            x={cell.x}
            y={cell.y + currentY}
            width={cellSize}
            height={segmentHeight}
            fill={contribution.color}
            stroke="rgba(27, 31, 35, 0.06)"
            strokeWidth="1px"
            rx="2"
            ry="2"
            style={{ cursor: showTooltip ? 'pointer' : 'default' }}
            onMouseEnter={(e) => handleCellHover(cell, e)}
            onMouseLeave={handleCellLeave}
          />
        );

        currentY += segmentHeight;
      });

      return <g key={index}>{segments}</g>;
    }
  };

  return (
    <StyledContributionGraph width={width} height={height}>
      <svg width={width} height={height}>
        {/* Month labels */}
        {monthLabels.map((month, index) => (
          <text
            key={index}
            x={month.x}
            y={10}
            fontSize="10"
            fill="currentColor"
            opacity={0.6}
          >
            {month.label}
          </text>
        ))}

        {/* Day labels */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
          <text
            key={day}
            x={-10}
            y={20 + index * (cellSize + cellGap) + cellSize / 2}
            fontSize="9"
            fill="currentColor"
            opacity={0.6}
            textAnchor="end"
            dominantBaseline="middle"
          >
            {index % 2 === 1 ? day : ''} {/* Show only Mon, Wed, Fri */}
          </text>
        ))}

        {/* Contribution cells */}
        <g transform="translate(20, 20)">
          {cells.map((cell, index) => renderCell(cell, index))}
        </g>
      </svg>

      {/* Legend */}
      {showLegend && (
        <StyledLegend>
          <span>Less</span>
          {[0, 1, 2, 3, 4].map(level => (
            <div
              key={level}
              style={{
                width: cellSize,
                height: cellSize,
                backgroundColor: getLevelColor(level),
                border: '1px solid rgba(27,31,35,0.06)'
              }}
            />
          ))}
          <span>More</span>
        </StyledLegend>
      )}

      {/* Tooltip */}
      {tooltip && showTooltip && (
        <StyledTooltip
          style={{
            left: mousePosition.x,
            top: mousePosition.y
          }}
        >
          <div><strong>{tooltip.totalValue} total sales</strong></div>
          <div>on {tooltip.formattedDate}</div>
          {tooltip.contributions.length > 0 && (
            <div style={{ marginTop: '8px' }}>
              {tooltip.contributions.map((contribution, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '6px', marginTop: '2px' }}>
                  <div
                    style={{
                      width: '12px',
                      height: '12px',
                      backgroundColor: contribution.color,
                      borderRadius: '2px'
                    }}
                  />
                  <span>{contribution.producer}: {contribution.value}</span>
                </div>
              ))}
            </div>
          )}
        </StyledTooltip>
      )}
    </StyledContributionGraph>
  );
};
