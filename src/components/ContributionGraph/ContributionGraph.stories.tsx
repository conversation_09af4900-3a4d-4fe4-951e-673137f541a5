import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Card } from '../Card';
import { ContributionGraph } from './ContributionGraph';
import { mockContributionData, stackedMockData, defaultProducers, highActivityData, lowActivityData, noActivityData } from './mock-data';

const meta: Meta<typeof ContributionGraph> = {
  title: 'Atoms/ContributionGraph',
  component: ContributionGraph,
  decorators: [
    (Story) => (
      <Card>
        <Story />
      </Card>
    ),
  ],
  argTypes: {
    mode: {
      control: { type: 'select' },
      options: ['single', 'stacked'],
    },
    colorScheme: {
      control: { type: 'select' },
      options: ['green', 'blue', 'purple', 'orange'],
    },
    producer: {
      control: { type: 'select' },
      options: ['Laurie', 'Taisei', 'Alex', 'Maria', 'John'],
    },
    cellSize: {
      control: { type: 'range', min: 8, max: 20, step: 1 },
    },
    cellGap: {
      control: { type: 'range', min: 1, max: 5, step: 1 },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ContributionGraph>;

// Stacked mode stories (new default)
export const StackedView: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    showTooltip: true,
    showLegend: true,
  },
};

export const Default: Story = {
  args: {
    data: mockContributionData,
    producers: [{ name: 'Laurie', color: '#2563eb' }],
    producer: 'Laurie',
    mode: 'single',
    colorScheme: 'green',
    showTooltip: true,
    showLegend: true,
  },
};

export const StackedCompact: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    cellSize: 8,
    cellGap: 1,
    showTooltip: true,
    showLegend: true,
  },
};

export const StackedCustomDateRange: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    startDate: '2024-06-01',
    endDate: '2024-12-31',
    showTooltip: true,
    showLegend: true,
  },
};

// Single producer mode stories
export const SingleProducerLaurie: Story = {
  args: {
    data: mockContributionData,
    producers: [{ name: 'Laurie', color: '#2563eb' }],
    producer: 'Laurie',
    mode: 'single',
    colorScheme: 'blue',
    showTooltip: true,
    showLegend: true,
  },
};

export const SingleProducerTaisei: Story = {
  args: {
    data: mockContributionData,
    producers: [{ name: 'Taisei', color: '#dc2626' }],
    producer: 'Taisei',
    mode: 'single',
    colorScheme: 'green',
    showTooltip: true,
    showLegend: true,
  },
};

export const NoTooltip: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    showTooltip: false,
    showLegend: true,
  },
};

export const NoLegend: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    showTooltip: true,
    showLegend: false,
  },
};

export const CustomSize: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    cellSize: 16,
    cellGap: 3,
    showTooltip: true,
    showLegend: true,
  },
};

export const CompactSize: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    cellSize: 8,
    cellGap: 1,
    showTooltip: true,
    showLegend: true,
  },
};

export const CustomDateRange: Story = {
  args: {
    data: stackedMockData,
    producers: defaultProducers,
    mode: 'stacked',
    startDate: '2024-06-01',
    endDate: '2024-12-31',
    showTooltip: true,
    showLegend: true,
  },
};

export const AllColorSchemes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
      <div>
        <h3>Green (GitHub style)</h3>
        <ContributionGraph
          data={mockContributionData}
          producers={[{ name: 'Laurie', color: '#2563eb' }]}
          producer="Laurie"
          mode="single"
          colorScheme="green"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Blue</h3>
        <ContributionGraph
          data={mockContributionData}
          producers={[{ name: 'Taisei', color: '#dc2626' }]}
          producer="Taisei"
          mode="single"
          colorScheme="blue"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Purple</h3>
        <ContributionGraph
          data={mockContributionData}
          producers={[{ name: 'Alex', color: '#16a34a' }]}
          producer="Alex"
          mode="single"
          colorScheme="purple"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Orange</h3>
        <ContributionGraph
          data={mockContributionData}
          producers={[{ name: 'Maria', color: '#ca8a04' }]}
          producer="Maria"
          mode="single"
          colorScheme="orange"
          showTooltip={true}
          showLegend={true}
        />
      </div>
    </div>
  ),
};

export const ModeComparison: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
      <div>
        <h3>Stacked View - All Producers Combined</h3>
        <p>Shows total sales with each producer's contribution stacked in each cell</p>
        <ContributionGraph
          data={stackedMockData}
          producers={defaultProducers}
          mode="stacked"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Single Producer View - Laurie Only</h3>
        <p>Shows intensity levels for a single producer</p>
        <ContributionGraph
          data={stackedMockData}
          producers={[{ name: 'Laurie', color: '#2563eb' }]}
          producer="Laurie"
          mode="single"
          colorScheme="blue"
          showTooltip={true}
          showLegend={true}
        />
      </div>
    </div>
  ),
};

export const ProducerBreakdown: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <h3>Combined Team Performance</h3>
        <ContributionGraph
          data={stackedMockData}
          producers={defaultProducers}
          mode="stacked"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px', marginTop: '16px' }}>
        <h4>Legend:</h4>
        {defaultProducers.map(producer => (
          <div key={producer.name} style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            <div
              style={{
                width: '16px',
                height: '16px',
                backgroundColor: producer.color,
                borderRadius: '3px'
              }}
            />
            <span>{producer.name}</span>
          </div>
        ))}
      </div>
    </div>
  ),
};
