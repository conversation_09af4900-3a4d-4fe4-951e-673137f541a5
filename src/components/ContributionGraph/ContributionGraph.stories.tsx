import type { Meta, StoryObj } from '@storybook/react';
import { Card } from '../Card';
import { ContributionGraph } from './ContributionGraph';
import { mockContributionData, highActivityData, lowActivityData, noActivityData } from './mock-data';

const meta: Meta<typeof ContributionGraph> = {
  title: 'Atoms/ContributionGraph',
  component: ContributionGraph,
  decorators: [
    (Story) => (
      <Card>
        <Story />
      </Card>
    ),
  ],
  argTypes: {
    colorScheme: {
      control: { type: 'select' },
      options: ['green', 'blue', 'purple', 'orange'],
    },
    producer: {
      control: { type: 'select' },
      options: ['<PERSON>', 'Taisei', 'Alex', '<PERSON>', '<PERSON>'],
    },
    cellSize: {
      control: { type: 'range', min: 8, max: 20, step: 1 },
    },
    cellGap: {
      control: { type: 'range', min: 1, max: 5, step: 1 },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ContributionGraph>;

export const Default: Story = {
  args: {
    data: mockContributionData,
    producer: '<PERSON>',
    colorScheme: 'green',
    showTooltip: true,
    showLegend: true,
  },
};

export const HighActivity: Story = {
  args: {
    data: mockContributionData,
    producer: 'Taisei',
    colorScheme: 'blue',
    showTooltip: true,
    showLegend: true,
  },
};

export const LowActivity: Story = {
  args: {
    data: mockContributionData,
    producer: 'John',
    colorScheme: 'purple',
    showTooltip: true,
    showLegend: true,
  },
};

export const NoTooltip: Story = {
  args: {
    data: mockContributionData,
    producer: 'Alex',
    colorScheme: 'orange',
    showTooltip: false,
    showLegend: true,
  },
};

export const NoLegend: Story = {
  args: {
    data: mockContributionData,
    producer: 'Maria',
    colorScheme: 'green',
    showTooltip: true,
    showLegend: false,
  },
};

export const CustomSize: Story = {
  args: {
    data: mockContributionData,
    producer: 'Laurie',
    colorScheme: 'blue',
    cellSize: 16,
    cellGap: 3,
    showTooltip: true,
    showLegend: true,
  },
};

export const CompactSize: Story = {
  args: {
    data: mockContributionData,
    producer: 'Taisei',
    colorScheme: 'purple',
    cellSize: 8,
    cellGap: 1,
    showTooltip: true,
    showLegend: true,
  },
};

export const CustomDateRange: Story = {
  args: {
    data: mockContributionData,
    producer: 'Alex',
    colorScheme: 'orange',
    startDate: '2024-06-01',
    endDate: '2024-12-31',
    showTooltip: true,
    showLegend: true,
  },
};

export const AllColorSchemes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
      <div>
        <h3>Green (GitHub style)</h3>
        <ContributionGraph
          data={mockContributionData}
          producer="Laurie"
          colorScheme="green"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Blue</h3>
        <ContributionGraph
          data={mockContributionData}
          producer="Taisei"
          colorScheme="blue"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Purple</h3>
        <ContributionGraph
          data={mockContributionData}
          producer="Alex"
          colorScheme="purple"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Orange</h3>
        <ContributionGraph
          data={mockContributionData}
          producer="Maria"
          colorScheme="orange"
          showTooltip={true}
          showLegend={true}
        />
      </div>
    </div>
  ),
};

export const ProducerComparison: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <h3>Laurie's Sales Activity</h3>
        <ContributionGraph
          data={mockContributionData}
          producer="Laurie"
          colorScheme="green"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Taisei's Sales Activity</h3>
        <ContributionGraph
          data={mockContributionData}
          producer="Taisei"
          colorScheme="blue"
          showTooltip={true}
          showLegend={true}
        />
      </div>
      <div>
        <h3>Alex's Sales Activity</h3>
        <ContributionGraph
          data={mockContributionData}
          producer="Alex"
          colorScheme="purple"
          showTooltip={true}
          showLegend={true}
        />
      </div>
    </div>
  ),
};
