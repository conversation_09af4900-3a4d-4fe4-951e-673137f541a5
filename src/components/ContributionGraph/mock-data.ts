import { format, subDays, addDays } from 'date-fns';
import { ContributionData, ProducerConfig } from './types';

// Generate mock sales data for the last year
export const generateMockContributionData = (): ContributionData[] => {
  const data: ContributionData[] = [];
  const producers = ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
  const endDate = new Date();
  const startDate = subDays(endDate, 364); // ~1 year of data

  let currentDate = startDate;

  while (currentDate <= endDate) {
    producers.forEach(producer => {
      // Generate realistic sales patterns
      const dayOfWeek = currentDate.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      const isHoliday = Math.random() < 0.05; // 5% chance of holiday

      let baseActivity = 0;

      // Different activity patterns per producer
      switch (producer) {
        case 'Laurie':
          baseActivity = isWeekend ? 0.2 : 0.8;
          break;
        case 'Taisei':
          baseActivity = isWeekend ? 0.1 : 0.9;
          break;
        case 'Alex':
          baseActivity = isWeekend ? 0.4 : 0.6;
          break;
        case 'Maria':
          baseActivity = isWeekend ? 0.3 : 0.7;
          break;
        case 'John':
          baseActivity = isWeekend ? 0.1 : 0.5;
          break;
      }

      // Reduce activity on holidays
      if (isHoliday) {
        baseActivity *= 0.1;
      }

      // Add some randomness
      const randomFactor = Math.random();
      const shouldHaveActivity = randomFactor < baseActivity;

      let value = 0;
      if (shouldHaveActivity) {
        // Generate sales count (0-20 range, with most values being lower)
        value = Math.floor(Math.random() * Math.random() * 20);
      }

      data.push({
        date: format(currentDate, 'yyyy-MM-dd'),
        value,
        producer
      });
    });

    currentDate = addDays(currentDate, 1);
  }

  return data;
};

// Pre-generated mock data for consistent stories
export const mockContributionData: ContributionData[] = generateMockContributionData();

// Sample data for specific scenarios
export const highActivityData: ContributionData[] = [
  { date: '2024-01-01', value: 15, producer: 'Laurie' },
  { date: '2024-01-02', value: 12, producer: 'Laurie' },
  { date: '2024-01-03', value: 18, producer: 'Laurie' },
  { date: '2024-01-04', value: 8, producer: 'Laurie' },
  { date: '2024-01-05', value: 22, producer: 'Laurie' },
  // Add more dates as needed...
];

export const lowActivityData: ContributionData[] = [
  { date: '2024-01-01', value: 1, producer: 'John' },
  { date: '2024-01-02', value: 0, producer: 'John' },
  { date: '2024-01-03', value: 2, producer: 'John' },
  { date: '2024-01-04', value: 0, producer: 'John' },
  { date: '2024-01-05', value: 1, producer: 'John' },
  // Add more dates as needed...
];

export const noActivityData: ContributionData[] = [
  { date: '2024-01-01', value: 0, producer: 'Inactive' },
  { date: '2024-01-02', value: 0, producer: 'Inactive' },
  { date: '2024-01-03', value: 0, producer: 'Inactive' },
  { date: '2024-01-04', value: 0, producer: 'Inactive' },
  { date: '2024-01-05', value: 0, producer: 'Inactive' },
  // Add more dates as needed...
];

// Producer configurations with colors
export const defaultProducers: ProducerConfig[] = [
  { name: 'Laurie', color: '#2563eb' }, // Blue
  { name: 'Taisei', color: '#dc2626' }, // Red
  { name: 'Alex', color: '#16a34a' }, // Green
  { name: 'Maria', color: '#ca8a04' }, // Yellow
  { name: 'John', color: '#9333ea' }, // Purple
];

// Generate enhanced mock data with overlapping contributions
export const generateStackedMockData = (): ContributionData[] => {
  const data: ContributionData[] = [];
  const endDate = new Date();
  const startDate = subDays(endDate, 364);

  let currentDate = startDate;

  while (currentDate <= endDate) {
    const dateStr = format(currentDate, 'yyyy-MM-dd');
    const dayOfWeek = currentDate.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;

    defaultProducers.forEach(producer => {
      // Different activity patterns per producer
      let baseActivity = 0;

      switch (producer.name) {
        case 'Laurie':
          baseActivity = isWeekend ? 0.3 : 0.8;
          break;
        case 'Taisei':
          baseActivity = isWeekend ? 0.2 : 0.9;
          break;
        case 'Alex':
          baseActivity = isWeekend ? 0.5 : 0.7;
          break;
        case 'Maria':
          baseActivity = isWeekend ? 0.4 : 0.6;
          break;
        case 'John':
          baseActivity = isWeekend ? 0.1 : 0.5;
          break;
      }

      // Add some randomness and seasonal variation
      const randomFactor = Math.random();
      const monthVariation = Math.sin((currentDate.getMonth() / 12) * Math.PI * 2) * 0.3 + 1;
      const shouldHaveActivity = randomFactor < (baseActivity * monthVariation);

      let value = 0;
      if (shouldHaveActivity) {
        // Generate sales count with different ranges per producer
        const maxSales = producer.name === 'Taisei' ? 25 : producer.name === 'Laurie' ? 20 : 15;
        value = Math.floor(Math.random() * Math.random() * maxSales);
      }

      if (value > 0) {
        data.push({
          date: dateStr,
          value,
          producer: producer.name
        });
      }
    });

    currentDate = addDays(currentDate, 1);
  }

  return data;
};

// Pre-generated stacked mock data
export const stackedMockData: ContributionData[] = generateStackedMockData();
