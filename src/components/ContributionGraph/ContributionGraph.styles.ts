import styled from 'styled-components';

export const StyledContributionGraph = styled.div<{ width: number; height: number }>`
  position: relative;
  display: inline-block;
  font-family: ${({ theme }) => theme.fonts.body};
  color: ${({ theme }) => theme.colors.text};
  
  svg {
    overflow: visible;
  }
  
  text {
    font-family: ${({ theme }) => theme.fonts.body};
    fill: ${({ theme }) => theme.colors.textMuted};
  }
`;

export const StyledCell = styled.rect`
  stroke: rgba(27, 31, 35, 0.06);
  stroke-width: 1px;
  rx: 2px;
  ry: 2px;
  transition: all 0.2s ease;
  
  &:hover {
    stroke: ${({ theme }) => theme.colors.text};
    stroke-width: 1px;
    transform: scale(1.1);
    transform-origin: center;
  }
`;

export const StyledTooltip = styled.div`
  position: fixed;
  background: ${({ theme }) => theme.colors.contentBg};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.sizes.borderRadius};
  padding: 8px 12px;
  font-size: ${({ theme }) => theme.sizes.fonts.sm};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: ${({ theme }) => theme.zIndices.toast};
  pointer-events: none;
  white-space: nowrap;
  
  div {
    margin: 2px 0;
    
    &:first-child {
      margin-top: 0;
    }
    
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const StyledLegend = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 16px;
  font-size: ${({ theme }) => theme.sizes.fonts.xs};
  color: ${({ theme }) => theme.colors.textMuted};
  
  span {
    margin: 0 4px;
  }
  
  div {
    border-radius: 2px;
    border: 1px solid ${({ theme }) => theme.colors.border};
  }
`;
