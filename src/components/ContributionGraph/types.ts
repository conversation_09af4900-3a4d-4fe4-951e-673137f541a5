export interface ContributionData {
  date: string; // ISO date string (YYYY-MM-DD)
  value: number; // Sales count for this date
  producer: string; // Producer name
}

export interface ContributionGraphProps {
  data: ContributionData[];
  producer: string; // Which producer to show data for
  width?: number;
  height?: number;
  cellSize?: number;
  cellGap?: number;
  showTooltip?: boolean;
  showLegend?: boolean;
  startDate?: string; // ISO date string for start of graph period
  endDate?: string; // ISO date string for end of graph period
  colorScheme?: 'green' | 'blue' | 'purple' | 'orange';
  maxValue?: number; // Override max value for color scaling
}

export interface ContributionCell {
  date: string;
  value: number;
  level: number; // 0-4 intensity level for coloring
  x: number;
  y: number;
}

export interface TooltipData {
  date: string;
  value: number;
  producer: string;
  formattedDate: string;
}
