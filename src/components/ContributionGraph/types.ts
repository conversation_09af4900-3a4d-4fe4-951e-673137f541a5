export interface ContributionData {
  date: string; // ISO date string (YYYY-MM-DD)
  value: number; // Sales count for this date
  producer: string; // Producer name
}

export interface ProducerConfig {
  name: string;
  color: string;
}

export interface ContributionGraphProps {
  data: ContributionData[];
  producers: ProducerConfig[]; // Configuration for all producers to display
  mode?: 'single' | 'stacked'; // Single producer or stacked multi-producer view
  producer?: string; // Which producer to show data for (only used in single mode)
  width?: number;
  height?: number;
  cellSize?: number;
  cellGap?: number;
  showTooltip?: boolean;
  showLegend?: boolean;
  startDate?: string; // ISO date string for start of graph period
  endDate?: string; // ISO date string for end of graph period
  colorScheme?: 'green' | 'blue' | 'purple' | 'orange'; // Only used in single mode
  maxValue?: number; // Override max value for color scaling
}

export interface ProducerContribution {
  producer: string;
  value: number;
  color: string;
}

export interface ContributionCell {
  date: string;
  totalValue: number;
  contributions: ProducerContribution[]; // Breakdown by producer
  level: number; // 0-4 intensity level for coloring (used in single mode)
  x: number;
  y: number;
}

export interface TooltipData {
  date: string;
  totalValue: number;
  contributions: ProducerContribution[];
  formattedDate: string;
}
