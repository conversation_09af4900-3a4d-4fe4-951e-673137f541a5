import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Avatar, AvatarFallback, AvatarImage } from './Avatar';

const meta: Meta<typeof Avatar> = {
  title: 'Atoms/Avatar',
  component: Avatar,
  render: (args) => {
    return (
      <Avatar>
        <AvatarImage src="https://i.pravatar.cc/40" />
        <AvatarFallback>LC</AvatarFallback>
      </Avatar>
    );
  },
  decorators: [
    (Story) => (
      <div style={{ width: '320px' }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Avatar>;

export const Default: Story = {};
