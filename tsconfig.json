{
  "include": ["./src/**/*", "./*.ts", "./vite.config.ts", "index.es.js", "index.umd.js", "./postbuild.js", "./.eslintrc.js"],
  "compilerOptions": {
    "outDir": "dist",
    "target": "ES6",
    "module": "ES2022",
    "lib": ["dom", "ES2017", "ES2019"],
    // "allowJs": true,
    // "checkJs": true,
    "types": ["vite/client"],
    "jsx": "react-jsx",
    "strict": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "declaration": true,
    "declarationMap": true,
    "emitDecoratorMetadata": true,
    "noEmit": false,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "resolveJsonModule": true
  }
}
